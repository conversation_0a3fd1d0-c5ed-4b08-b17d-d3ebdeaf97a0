import {
  createSerializer,
  parse<PERSON><PERSON>nteger,
  Url<PERSON><PERSON><PERSON>,
  createLoader,
} from "nuqs/server";

export type UserPaginationType = "page" | "limit";

export const userPaginationSearchParams = {
  page: parseAsInteger.withDefault(1),
  limit: parseAsInteger.withDefault(10),
} satisfies Record<UserPaginationType, any>;

export const userPaginationUrlKeys: UrlKeys<typeof userPaginationSearchParams> =
  {
    page: "page",
    limit: "limit",
  };

export const userPaginationLoader = createLoader(userPaginationSearchParams, {
  urlKeys: userPaginationUrlKeys,
});

export const serializeUserPaginationParams = createSerializer(
  userPaginationSearchParams,
  {
    urlKeys: userPaginationUrlKeys,
  },
);
