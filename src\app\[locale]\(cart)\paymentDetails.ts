import { TFunctionCheckoutForm } from "@/i18n/types";
import { paymentMethods } from "./model";
import { PAYMENT_COORDINATES } from "./paymentConstants";

export type PaymentDetail = {
  label: string;
  value?: string;
};

export type PaymentDetailsMap = {
  [key in keyof typeof paymentMethods]: PaymentDetail[];
};

export const getPaymentDetails = (
  t: TFunctionCheckoutForm,
  paymentMethod: keyof typeof paymentMethods,
): PaymentDetail[] => {
  const paymentDetailsMap: PaymentDetailsMap = {
    [paymentMethods.BANK_TRANSFER]: [
      {
        label: t("BANK_TRANSFER.entityLabel"),
        value: PAYMENT_COORDINATES.entity,
      },
      {
        label: t("BANK_TRANSFER.nifLabel"),
        value: PAYMENT_COORDINATES.nif,
      },
      {
        label: t("BANK_TRANSFER.bankNameLabel"),
        value: PAYMENT_COORDINATES.bank,
      },
      {
        label: t("BANK_TRANSFER.accountNumberLabel"),
        value: PAYMENT_COORDINATES.account,
      },
      {
        label: t("BANK_TRANSFER.ibanLabel"),
        value: PAYMENT_COORDINATES.iban,
      },
    ],
    [paymentMethods.MULTICAIXA_EXPRESS]: [
      {
        label: t("MULTICAIXA_EXPRESS.numberLabel"),
        value: "9XX XXX XXX",
      },
    ],
    [paymentMethods.PAY_IN_STORE]: [],
  };

  return paymentDetailsMap[paymentMethod] || [];
};

/**
 * Get payment details for a specific payment method
 * @param t - Translation function from useTranslations or getTranslations
 * @param paymentMethod - The payment method to get details for
 * @returns Array of payment details
 */
export function getPaymentDetailsWithNotes(
  t: TFunctionCheckoutForm,
  paymentMethod: keyof typeof paymentMethods,
): PaymentDetail[] {
  const paymentDetailsMap: PaymentDetailsMap = {
    [paymentMethods.BANK_TRANSFER]: [
      {
        label: t("BANK_TRANSFER.entityLabel"),
        value: PAYMENT_COORDINATES.entity,
      },
      {
        label: t("BANK_TRANSFER.nifLabel"),
        value: PAYMENT_COORDINATES.nif,
      },
      {
        label: t("BANK_TRANSFER.bankNameLabel"),
        value: PAYMENT_COORDINATES.bank,
      },
      {
        label: t("BANK_TRANSFER.accountNumberLabel"),
        value: PAYMENT_COORDINATES.account,
      },
      {
        label: t("BANK_TRANSFER.ibanLabel"),
        value: PAYMENT_COORDINATES.iban,
      },
      {
        label: t("BANK_TRANSFER.confirmationNote"),
      },
      {
        label: t("BANK_TRANSFER.paymentDeadlineNote"),
      },
    ],
    [paymentMethods.MULTICAIXA_EXPRESS]: [
      {
        label: t("MULTICAIXA_EXPRESS.numberLabel"),
        value: "9XX XXX XXX",
      },
      {
        label: t("MULTICAIXA_EXPRESS.confirmationNote"),
      },
      {
        label: t("MULTICAIXA_EXPRESS.paymentDeadlineNote"),
      },
    ],
    [paymentMethods.PAY_IN_STORE]: [
      {
        label: t("PAY_IN_STORE.confirmationNote"),
      },
      {
        label: t("PAY_IN_STORE.paymentDeadlineNote"),
      },
    ],
  };

  return paymentDetailsMap[paymentMethod] || [];
}

/**
 * Get all payment details mapped by payment method
 * @param t - Translation function from useTranslations or getTranslations
 * @returns Complete payment details map
 */
export function getAllPaymentDetails(
  t: TFunctionCheckoutForm,
): PaymentDetailsMap {
  return {
    [paymentMethods.BANK_TRANSFER]: getPaymentDetailsWithNotes(
      t,
      paymentMethods.BANK_TRANSFER,
    ),
    [paymentMethods.MULTICAIXA_EXPRESS]: getPaymentDetailsWithNotes(
      t,
      paymentMethods.MULTICAIXA_EXPRESS,
    ),
    [paymentMethods.PAY_IN_STORE]: getPaymentDetailsWithNotes(
      t,
      paymentMethods.PAY_IN_STORE,
    ),
  };
}

export const getPaymentNotes = (
  t: TFunctionCheckoutForm,
  paymentMethod: keyof typeof paymentMethods,
) => {
  return [
    {
      label: t(`${paymentMethod}.confirmationNote`),
    },
    {
      label: t(`${paymentMethod}.paymentDeadlineNote`),
    },
  ];
};
