import AuthCard from "@/app/[locale]/(auth)/AuthCard";
import SignOnForm from "@/app/[locale]/(auth)/SignOnForm";
import { linksObj } from "@/app/links";
import { redirect } from "next/navigation";
import { getUserIfExists } from "../user-dal";

const SignOnPage = async () => {
  const user = await getUserIfExists();

  if (user?.id) {
    return redirect(linksObj.profile.href);
  }

  return (
    <main className="container-full flex min-h-[80vh] flex-col">
      <AuthCard mode="sign-on">
        <SignOnForm />
      </AuthCard>
    </main>
  );
};

export default SignOnPage;
