"use server";

import { auth } from "@/lib/auth/server";
import { revalidatePath } from "next/cache";
import { getTranslations } from "next-intl/server";
import type { ActionResult } from "@/lib/types";
import { LOCALES } from "@/i18n/routing";
import { linksObj } from "@/app/links";
import { headers } from "next/headers";
import { requireAdminUser } from "../../(auth)/user-dal";

export const banUser = async ({
  userId,
}: {
  userId: string;
}): Promise<ActionResult> => {
  const t = await getTranslations("CMS.users");

  try {
    await requireAdminUser();
    await auth.api.banUser({ headers: await headers(), body: { userId } });
    for (const loc of LOCALES) {
      revalidatePath(`/${loc}${linksObj.cms.users.href}`);
    }
    return { success: true, message: t("banSuccess") };
  } catch (error) {
    console.error(`Error banning user ${userId}:`, error);
    const errorMessage =
      error instanceof Error ? error.message : t("genericError");
    return { success: false, message: t("banError", { error: errorMessage }) };
  }
};

export const unbanUser = async ({
  userId,
}: {
  userId: string;
}): Promise<ActionResult> => {
  const t = await getTranslations("CMS.users");

  try {
    await requireAdminUser();
    await auth.api.unbanUser({ headers: await headers(), body: { userId } });
    for (const loc of LOCALES) {
      revalidatePath(`/${loc}${linksObj.cms.users.href}`);
    }
    return { success: true, message: t("unbanSuccess") };
  } catch (error) {
    console.error(`Error unbanning user ${userId}:`, error);
    const errorMessage =
      error instanceof Error ? error.message : t("genericError");
    return {
      success: false,
      message: t("unbanError", { error: errorMessage }),
    };
  }
};

export const revokeAllUserSessions = async ({
  userId,
}: {
  userId: string;
}): Promise<ActionResult> => {
  const t = await getTranslations("CMS.users");
  try {
    await requireAdminUser();
    await auth.api.revokeUserSessions({
      headers: await headers(),
      body: { userId },
    });
    for (const loc of LOCALES) {
      revalidatePath(`/${loc}${linksObj.cms.users.href}`);
    }
    return { success: true, message: t("revokeSessionsSuccess") };
  } catch (error) {
    console.error(`Error revoking sessions for user ${userId}:`, error);
    const errorMessage =
      error instanceof Error ? error.message : t("genericError");
    return {
      success: false,
      message: t("revokeSessionsError", { error: errorMessage }),
    };
  }
};

export const removeUser = async ({
  userId,
}: {
  userId: string;
}): Promise<ActionResult> => {
  const t = await getTranslations("CMS.users");

  try {
    await requireAdminUser();
    await auth.api.removeUser({ headers: await headers(), body: { userId } });
    for (const loc of LOCALES) {
      revalidatePath(`/${loc}${linksObj.cms.users.href}`);
    }
    return { success: true, message: t("removeUserSuccess") };
  } catch (error) {
    console.error(`Error removing user ${userId}:`, error);
    const errorMessage =
      error instanceof Error ? error.message : t("genericError");
    return {
      success: false,
      message: t("removeUserError", { error: errorMessage }),
    };
  }
};
