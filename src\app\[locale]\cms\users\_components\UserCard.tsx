import { UserType } from "@/app/[locale]/(auth)/model";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Mail, Phone } from "lucide-react";
import { getTranslations } from "next-intl/server";
import { ReactNode } from "react";

const statusColors = {
  // PENDING: "bg-yellow-100 text-yellow-800",
  ACTIVE: "bg-green-100 text-green-800",
  BANNED: "bg-red-100 text-red-800",
} as const;

type Props = {
  user: UserType;
  children?: ReactNode;
  isAdmin: boolean;
};

const UserCard = async ({ user, isAdmin }: Props) => {
  const t = await getTranslations("CMS.users");

  const info = [
    { value: user.email, label: t("email"), Icon: Mail },
    {
      value: user.phoneNumber,
      label: t("phone"),
      Icon: Phone,
    },
    {
      value: user.role,
      label: t("role"),
      Icon: undefined,
    },
    {
      value: user.createdAt.toLocaleDateString(),
      label: t("createdAt"),
      // Icon: Calendar,
    },
  ];

  return (
    <Card className="relative gap-2">
      <CardHeader>
        <div className="flex flex-wrap items-center justify-between gap-2">
          <div className="min-w-0 flex-1">
            <CardTitle className="text-base text-wrap">
              {t("name")}: {user.firstName} {user.lastName}
            </CardTitle>
          </div>
          <div className="flex items-center gap-2">
            <Badge
              className={cn(
                "text-xs font-semibold",
                user.banned ? statusColors.BANNED : statusColors.ACTIVE,
              )}
            >
              {user.banned ? t("banned") : t("active")}
            </Badge>
            {/* <OrderUserActions order={order} isAdmin={isAdmin} /> */}
          </div>
        </div>
      </CardHeader>

      <CardContent className="flex flex-col gap-4">
        <ul className="flex flex-wrap gap-4 text-sm sm:text-base">
          {info.map((item, index) =>
            item.value ? (
              <li key={index} className="flex items-center gap-2">
                {item.Icon ? (
                  <item.Icon className="text-muted-foreground size-4" />
                ) : (
                  <span className="text-muted-foreground">{item.label}:</span>
                )}
                <span>{item.value}</span>
              </li>
            ) : null,
          )}
        </ul>
      </CardContent>
    </Card>
  );
};

export default UserCard;
