"use client";

import Text from "@/components/Text";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { CheckCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import { paymentMethods } from "./model";
import { getPaymentNotes } from "./paymentDetails";

type SuccessfulOrderMessageProps = {
  className?: string;
  orderId: string;
  paymentMethod: keyof typeof paymentMethods;
  onClose: () => void;
};

const SuccessfulOrderMessage = ({
  className,
  orderId,
  paymentMethod,
  onClose,
}: SuccessfulOrderMessageProps) => {
  const t = useTranslations("CheckoutForm");
  const tSuccess = useTranslations("OrderSuccess");

  const paymentNotes = getPaymentNotes(t, paymentMethod);

  return (
    <section
      className={cn(
        "mx-auto flex max-w-lg flex-col items-center gap-6 py-8",
        className,
      )}
    >
      <article className="grid gap-x-4 max-sm:justify-items-center max-sm:gap-y-4 max-sm:text-center sm:grid-cols-[auto_auto] sm:grid-rows-[auto_auto]">
        <span className="row-span-full flex aspect-square w-16 items-center justify-center rounded-full bg-green-100">
          <CheckCircle className="size-8 text-green-600" />
        </span>
        <Text as="h3" className="text-secondary text-2xl font-semibold">
          {tSuccess("title")}
        </Text>
        <Text as="p" size="sm">
          {tSuccess("subtitle")}{" "}
          <span className="font-semibold">{orderId}</span>
        </Text>
      </article>

      <article className="border-muted flex w-full flex-col gap-4 rounded-lg border p-6">
        <Text size="sm" className="text-secondary font-semibold">
          {tSuccess("confirmationTitle")}
        </Text>
        <ul className="flex flex-col gap-4 text-left">
          {paymentNotes.map((note) => (
            <li key={note.label} className="flex flex-col gap-2">
              <Text size="sm">{note.label}</Text>
            </li>
          ))}
        </ul>
      </article>

      <Button onClick={onClose} className="mx-auto w-fit">
        {tSuccess("closeButton")}
      </Button>
    </section>
  );
};

export default SuccessfulOrderMessage;
