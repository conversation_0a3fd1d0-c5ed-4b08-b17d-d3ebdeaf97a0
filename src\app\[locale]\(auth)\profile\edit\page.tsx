import AuthCard from "@/app/[locale]/(auth)/AuthCard";
import { ProfileFormType } from "@/app/[locale]/(auth)/model";
import ProfileForm from "@/app/[locale]/(auth)/ProfileForm";
import { linksObj } from "@/app/links";
import { redirect } from "next/navigation";
import { getUserIfExists } from "../../user-dal";

const ProfileEditPage = async () => {
  const user = await getUserIfExists();

  if (!user?.id) {
    return redirect(linksObj.signOn.href);
  }

  const userData: ProfileFormType = {
    firstName: user.name?.split(" ")[0] ?? "",
    lastName: user.name?.split(" ")[1] ?? "",
    phoneNumber: user.phoneNumber ?? "",
  };

  return (
    <main className="container-full flex min-h-[80vh] flex-col">
      <AuthCard mode="profile">
        <ProfileForm userData={userData} />
      </AuthCard>
    </main>
  );
};

export default ProfileEditPage;
