Stack trace:
Frame         Function      Args
0007FFFF9DE0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CE0) msys-2.0.dll+0x1FE8E
0007FFFF9DE0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0B8) msys-2.0.dll+0x67F9
0007FFFF9DE0  000210046832 (000210286019, 0007FFFF9C98, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DE0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DE0  000210068E24 (0007FFFF9DF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0C0  00021006A225 (0007FFFF9DF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDF8FA0000 ntdll.dll
7FFDF7A20000 KERNEL32.DLL
7FFDF6590000 KERNELBASE.dll
7FFDF8840000 USER32.dll
7FFDF6290000 win32u.dll
7FFDF6DA0000 GDI32.dll
7FFDF6150000 gdi32full.dll
7FFDF6440000 msvcp_win.dll
7FFDF6B10000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDF8780000 advapi32.dll
7FFDF82B0000 msvcrt.dll
7FFDF86D0000 sechost.dll
7FFDF7C80000 RPCRT4.dll
7FFDF56E0000 CRYPTBASE.DLL
7FFDF64F0000 bcryptPrimitives.dll
7FFDF8A20000 IMM32.DLL
