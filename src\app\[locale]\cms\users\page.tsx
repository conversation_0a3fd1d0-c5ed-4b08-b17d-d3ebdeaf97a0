import Text from "@/components/Text";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { User } from "lucide-react";
import { getTranslations } from "next-intl/server";
import UserCard from "./_components/UserCard";
import { getUsersData } from "./controller";
import { userPaginationLoader } from "./userSearchParams";

interface UsersPageProps {
  params: Promise<{ [key: string]: string | string[] | undefined }>;
}

async function LoadingUsersFallback() {
  const t = await getTranslations("CMS.users");
  return <Text>{t("loading")}</Text>;
}

export default async function CMSUsersPage({ params }: UsersPageProps) {
  const t = await getTranslations("CMS.users");
  const parsedParams = userPaginationLoader(await params);
  const { users } = await getUsersData(parsedParams);

  return (
    <main className="flex flex-1 flex-col gap-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">{t("users")}</CardTitle>
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <User className="text-muted-foreground mb-4 h-12 w-12" />
              <Text as="h3" size="lg" className="mb-2 font-semibold">
                {t("noUsers")}
              </Text>
              <Text
                as="p"
                size="sm"
                className="text-muted-foreground text-center"
              >
                {t("noUsersDescription")}
              </Text>
            </div>
          ) : (
            <article className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {users.map((user) => (
                <UserCard key={user.id} user={user} isAdmin />
              ))}
            </article>
          )}
        </CardContent>
      </Card>
    </main>
  );
}
