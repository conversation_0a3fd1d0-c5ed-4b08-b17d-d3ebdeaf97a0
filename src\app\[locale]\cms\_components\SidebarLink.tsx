"use client";

import { Link, usePathname } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface SidebarLinkProps {
  href: string;
  children: ReactNode;
}

export default function SidebarLink({ href, children }: SidebarLinkProps) {
  const pathname = usePathname();

  const isActive = pathname === href || pathname.startsWith(`${href}/`);

  return (
    <Link
      href={href}
      className={cn(
        "hocus:bg-primary-foreground/20 hocus:text-primary hocus:px-4 w-full rounded-md px-0 py-2 font-medium text-current duration-300 ease-out",
        {
          "bg-primary hocus:bg-primary hocus:text-accent text-accent px-4":
            isActive,
        },
      )}
    >
      {children}
    </Link>
  );
}
