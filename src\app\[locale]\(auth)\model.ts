import { TFunctionUser } from "@/i18n/types";
import { authClient } from "@/lib/auth/client";
import { z } from "zod";

export const phoneNumberSchema = z
  .string()
  .refine(
    (value) => !value || /^\+\d{3}\d{9}$/.test(value),
    "Número de telefone inválido",
  );

export const profileSchema = z.object({
  firstName: z
    .string()
    .min(2, { message: "Nome deve ter pelo menos 2 caracteres." })
    .max(30, { message: "Nome deve ter no máximo 30 caracteres." })
    .refine((value) => !/\s/.test(value), "Espaços não são permitidos"),
  lastName: z
    .string()
    .min(2, { message: "Sobrenome deve ter pelo menos 2 caracteres." })
    .max(30, { message: "Sobrenome deve ter no máximo 30 caracteres." })
    .refine((value) => !/\s/.test(value), "Espaços não são permitidos"),
  phoneNumber: phoneNumberSchema.optional().nullable(),
});
export type ProfileFormType = z.infer<typeof profileSchema>;

export const signOnSchema = z.object({
  email: z.string().email({ message: "Endereço de email inválido" }),
});
export type SignOnFormType = z.infer<typeof signOnSchema>;

export const otpSchema = signOnSchema.extend({
  otp: z.string().min(1, "OTP é obrigatório").max(6, "OTP inválido"),
});

export type OtpFormType = z.infer<typeof otpSchema>;

const UserRoles = ["admin", "user"] as const;

export const userSchema = (t: TFunctionUser) =>
  profileSchema.extend({
    id: z.string(),
    email: z.string().email({ message: t("emailInvalid") }),
    role: z.enum(UserRoles, { message: t("roleInvalid") }),
    banned: z.boolean().optional().default(false),
    createdAt: z.date(),
    updatedAt: z.date(),
  });

export type UserType = z.infer<ReturnType<typeof userSchema>>;

export const fromAuthToUser = ({
  authUser,
  t,
}: {
  authUser: NonNullable<
    ReturnType<typeof authClient.useSession>["data"]
  >["user"];
  t: TFunctionUser;
}) => {
  if (!authUser) throw new Error("User data is not available");

  const user = {
    ...authUser,
    firstName: authUser.name?.split(" ")[0] ?? "",
    lastName: authUser.name?.split(" ")[1] ?? "",
  };

  return userSchema(t).parse(user);
};
