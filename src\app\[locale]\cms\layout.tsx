import { ReactNode } from "react";
import SidebarLink from "./_components/SidebarLink";
import { getTranslations } from "next-intl/server";
import { linksObj } from "@/app/links";
import { Separator } from "@/components/ui/separator";

export default async function CMSLayout({ children }: { children: ReactNode }) {
  const t = await getTranslations("CMS.sidebar");

  return (
    <div className="container-full relative flex gap-10 bg-white">
      <aside className="sticky top-0 flex w-[20%] flex-shrink-0 flex-col gap-5">
        <header>
          <h1 className="text-2xl font-bold">{t("title")}</h1>
        </header>
        <Separator />
        <nav className="flex flex-col gap-2.5">
          <SidebarLink href={linksObj.cms.dashboard.href}>
            {t("dashboard")}
          </SidebarLink>
          <SidebarLink href={linksObj.cms.users.href}>{t("users")}</SidebarLink>
        </nav>
      </aside>
      {children}
    </div>
  );
}
