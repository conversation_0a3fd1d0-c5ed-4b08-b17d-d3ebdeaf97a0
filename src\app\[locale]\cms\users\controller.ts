import "server-only";
import { auth } from "@/lib/auth/server";
import { headers } from "next/headers";
import { requireAdminUser } from "../../(auth)/user-dal";
import { cache } from "react";
import { UserType, fromAuthToUser } from "../../(auth)/model";
import { getTranslations } from "next-intl/server";

export interface GetUsersDataResponse {
  users: UserType[];
  total: number;
  page: number;
  limit: number;
}

export const getUsersData = cache(
  async ({
    page = 1,
    limit = 100,
  }: {
    page?: number;
    limit?: number;
  }): Promise<GetUsersDataResponse> => {
    const offset = (page - 1) * limit;

    try {
      await requireAdminUser();
      const t = await getTranslations("User");

      const response = await auth.api.listUsers({
        headers: await headers(),
        query: {
          limit,
          offset,
        },
      });

      if (!response?.users?.[0]) {
        return { users: [], total: 0, page, limit };
      }

      const transformedUsers = response.users.map((user) => {
        return fromAuthToUser({
          authUser: {
            ...user,
            banned: user.banned || false,
          },
          t,
        });
      });

      return { users: transformedUsers, total: response.total, page, limit };
    } catch (error) {
      console.error("Failed to fetch users in controller:", error);
      return { users: [], total: 0, page, limit };
    }
  },
);
