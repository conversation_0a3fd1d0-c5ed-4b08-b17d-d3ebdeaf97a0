export const linksObj = {
  home: { href: "/" },
  profile: { href: "/profile" },
  profileEdit: { href: "/profile/edit" },
  signOn: { href: "/sign-on" },
  cms: {
    dashboard: { href: "/cms/dashboard" },
    users: { href: "/cms/users" },
    // Example for a dynamic link if needed in the future:
    // userProfile: (userId: string) => ({ href: `/cms/users/${userId}` }),
  },
  // other top-level categories can be added here, for example:
  // store: {
  //   product: (productId: string) => ({ href: `/products/${productId}` }),
  //   cart: { href: "/cart" },
  // }
};

// The old LINKS constant is now removed.
